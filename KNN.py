import pandas as pd
import numpy as np 

def NormalizePoints():
    role_col = ['user_id', 'movie_id','rating']

    ratings = pd.read_csv('u.data',sep = '\t', names = role_col,usecols=range(3))

    print(ratings.head())

    movieProperties = ratings.groupby('movie_id').agg({'rating' : [np.size , np.mean]})
    print(movieProperties.head())

    movieNumRating = pd.DataFrame(movieProperties['rating']['size'])
    print(movieNumRating.head())

    movieNormalizedNumRating = movieNumRating.apply(lambda x: (x - np.min(x)) / (np.max(x) - np.min(x)))
    print(movieNormalizedNumRating.head())
    

    movieDict = {}

    with open ('u.item') as f :
         temp = ''
         for line in f : 
              fields = line.rstrip('\n').split('|')
              movieID = fields[0] 
              name = fields[1]
              genres = fields[5:25]
              genres = map(int,genres)
              movieDict[movieID] = (name, genres, movieNormalizedNumRating.loc[movieID].get('size'),movieProperties.loc[movieID].rating['mean'])


    print(movieDict[1])

                                    


                                               
                                               

def test():
    array1 = np.array(range(0,36)).reshape(9,4)
    print(array1)

    colNames = ['a','b','c','d']
    df1 = pd.DataFrame(array1, columns = colNames)
    df1['b'] = df1['b'].apply(lambda x: x *x)
    print(df1)
    df1_mod1 = df1.apply(lambda x : (x - np.min(x)) / (np.max(x) - np.min(x)))
    # df1_mod1 = df1.apply(costumFunction1, axis = 1)
    print(df1_mod1)

def costumFunction1(row):
        return row['a'] + row['b']


NormalizePoints()
